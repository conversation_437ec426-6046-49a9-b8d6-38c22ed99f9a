["tests/test_client_api.py::TestClientAPI::test_create_client", "tests/test_client_api.py::TestClientAPI::test_create_client_invalid_nip", "tests/test_client_api.py::TestClientAPI::test_create_client_without_nip", "tests/test_client_api.py::TestClientAPI::test_delete_client", "tests/test_client_api.py::TestClientAPI::test_delete_client_not_found", "tests/test_client_api.py::TestClientAPI::test_get_client_by_id", "tests/test_client_api.py::TestClientAPI::test_get_client_by_id_not_found", "tests/test_client_api.py::TestClientAPI::test_get_clients_empty", "tests/test_client_api.py::TestClientAPI::test_get_clients_summary", "tests/test_client_api.py::TestClientAPI::test_get_clients_with_data", "tests/test_client_api.py::TestClientAPI::test_get_clients_with_pagination", "tests/test_client_api.py::TestClientAPI::test_get_clients_with_search", "tests/test_client_api.py::TestClientAPI::test_search_client_by_nip", "tests/test_client_api.py::TestClientAPI::test_update_client", "tests/test_client_api.py::TestClientAPI::test_update_client_not_found", "tests/test_client_service.py::TestClientService::test_create_client", "tests/test_client_service.py::TestClientService::test_create_client_invalid_nip", "tests/test_client_service.py::TestClientService::test_create_client_without_nip", "tests/test_client_service.py::TestClientService::test_delete_client", "tests/test_client_service.py::TestClientService::test_delete_client_not_found", "tests/test_client_service.py::TestClientService::test_get_client_by_id", "tests/test_client_service.py::TestClientService::test_get_client_by_id_not_found", "tests/test_client_service.py::TestClientService::test_get_clients_pagination", "tests/test_client_service.py::TestClientService::test_get_clients_search", "tests/test_client_service.py::TestClientService::test_search_clients_by_nip", "tests/test_client_service.py::TestClientService::test_update_client", "tests/test_client_service.py::TestClientService::test_update_client_not_found", "tests/test_client_service.py::TestPaginationUtils::test_calculate_pagination_info", "tests/test_company_api.py::TestCompanyAPI::test_create_company_profile", "tests/test_company_api.py::TestCompanyAPI::test_create_company_profile_invalid_nip", "tests/test_company_api.py::TestCompanyAPI::test_get_company_profile_exists", "tests/test_company_api.py::TestCompanyAPI::test_get_company_profile_not_exists", "tests/test_company_api.py::TestCompanyAPI::test_get_tax_settings_no_company", "tests/test_company_api.py::TestCompanyAPI::test_get_tax_settings_with_company", "tests/test_company_api.py::TestCompanyAPI::test_get_zus_settings_with_company", "tests/test_company_api.py::TestCompanyAPI::test_update_company_profile", "tests/test_company_api.py::TestCompanyAPI::test_update_company_profile_not_exists", "tests/test_company_api.py::TestCompanyAPI::test_update_tax_settings", "tests/test_company_service.py::TestCompanyService::test_create_company_profile", "tests/test_company_service.py::TestCompanyService::test_create_company_profile_invalid_nip", "tests/test_company_service.py::TestCompanyService::test_create_duplicate_company_profile", "tests/test_company_service.py::TestCompanyService::test_get_company_profile_empty", "tests/test_company_service.py::TestCompanyService::test_get_company_profile_exists", "tests/test_company_service.py::TestNIPValidation::test_invalid_nip_wrong_checksum", "tests/test_company_service.py::TestNIPValidation::test_invalid_nip_wrong_length", "tests/test_company_service.py::TestNIPValidation::test_nip_with_formatting", "tests/test_company_service.py::TestNIPValidation::test_valid_nip", "tests/test_expense_api.py::TestExpenseAPI::test_create_expense_future_date", "tests/test_expense_api.py::TestExpenseAPI::test_create_expense_invalid_data", "tests/test_expense_api.py::TestExpenseAPI::test_create_expense_with_gross_amount", "tests/test_expense_api.py::TestExpenseAPI::test_create_expense_with_net_amount", "tests/test_expense_api.py::TestExpenseAPI::test_delete_expense", "tests/test_expense_api.py::TestExpenseAPI::test_delete_expense_not_found", "tests/test_expense_api.py::TestExpenseAPI::test_get_expense_by_id", "tests/test_expense_api.py::TestExpenseAPI::test_get_expense_by_id_not_found", "tests/test_expense_api.py::TestExpenseAPI::test_get_expense_categories", "tests/test_expense_api.py::TestExpenseAPI::test_get_expense_summary", "tests/test_expense_api.py::TestExpenseAPI::test_get_expenses_empty", "tests/test_expense_api.py::TestExpenseAPI::test_get_expenses_with_data", "tests/test_expense_api.py::TestExpenseAPI::test_get_expenses_with_filters", "tests/test_expense_api.py::TestExpenseAPI::test_get_expenses_with_pagination", "tests/test_expense_api.py::TestExpenseAPI::test_get_expenses_with_search", "tests/test_expense_api.py::TestExpenseAPI::test_get_payment_methods", "tests/test_expense_api.py::TestExpenseAPI::test_update_expense", "tests/test_expense_api.py::TestExpenseAPI::test_update_expense_not_found", "tests/test_expense_service.py::TestExpenseCalculationUtils::test_calculate_vat_amounts_from_gross", "tests/test_expense_service.py::TestExpenseCalculationUtils::test_calculate_vat_amounts_from_gross_default_rate", "tests/test_expense_service.py::TestExpenseCalculationUtils::test_calculate_vat_amounts_from_net", "tests/test_expense_service.py::TestExpenseCalculationUtils::test_calculate_vat_amounts_invalid_input", "tests/test_expense_service.py::TestExpenseCalculationUtils::test_calculate_vat_amounts_with_rounding", "tests/test_expense_service.py::TestExpenseCalculationUtils::test_round_to_grosz", "tests/test_expense_service.py::TestExpenseService::test_create_expense_invalid_data", "tests/test_expense_service.py::TestExpenseService::test_create_expense_with_gross_amount", "tests/test_expense_service.py::TestExpenseService::test_create_expense_with_net_amount", "tests/test_expense_service.py::TestExpenseService::test_delete_expense", "tests/test_expense_service.py::TestExpenseService::test_delete_expense_not_found", "tests/test_expense_service.py::TestExpenseService::test_get_expense_by_id", "tests/test_expense_service.py::TestExpenseService::test_get_expense_by_id_not_found", "tests/test_expense_service.py::TestExpenseService::test_get_expenses_filter_by_category", "tests/test_expense_service.py::TestExpenseService::test_get_expenses_pagination", "tests/test_expense_service.py::TestExpenseService::test_get_expenses_search", "tests/test_expense_service.py::TestExpenseService::test_update_expense", "tests/test_expense_service.py::TestExpenseService::test_update_expense_not_found", "tests/test_expense_service.py::TestExpenseSummary::test_get_expense_summary", "tests/test_expense_service.py::TestPaginationUtils::test_calculate_pagination_info", "tests/test_invoice_api.py::TestInvoiceAPI::test_cancel_invoice_success", "tests/test_invoice_api.py::TestInvoiceAPI::test_create_invoice_client_not_found", "tests/test_invoice_api.py::TestInvoiceAPI::test_create_invoice_success", "tests/test_invoice_api.py::TestInvoiceAPI::test_create_invoice_validation_errors", "tests/test_invoice_api.py::TestInvoiceAPI::test_delete_invoice_not_draft", "tests/test_invoice_api.py::TestInvoiceAPI::test_delete_invoice_success", "tests/test_invoice_api.py::TestInvoiceAPI::test_get_invoice_by_number_success", "tests/test_invoice_api.py::TestInvoiceAPI::test_get_invoice_not_found", "tests/test_invoice_api.py::TestInvoiceAPI::test_get_invoice_success", "tests/test_invoice_api.py::TestInvoiceAPI::test_get_invoice_summary_empty", "tests/test_invoice_api.py::TestInvoiceAPI::test_get_invoice_summary_with_data", "tests/test_invoice_api.py::TestInvoiceAPI::test_issue_invoice_success", "tests/test_invoice_api.py::TestInvoiceAPI::test_list_invoices_empty", "tests/test_invoice_api.py::TestInvoiceAPI::test_list_invoices_with_data", "tests/test_invoice_api.py::TestInvoiceAPI::test_list_invoices_with_filters", "tests/test_invoice_api.py::TestInvoiceAPI::test_mark_invoice_paid_success", "tests/test_invoice_api.py::TestInvoiceAPI::test_update_invoice_not_draft", "tests/test_invoice_api.py::TestInvoiceAPI::test_update_invoice_success", "tests/test_invoice_service.py::TestInvoiceCalculations::test_calculate_due_date", "tests/test_invoice_service.py::TestInvoiceCalculations::test_calculate_item_totals", "tests/test_invoice_service.py::TestInvoiceCalculations::test_round_to_grosz", "tests/test_invoice_service.py::TestInvoiceCreation::test_create_invoice_client_not_found", "tests/test_invoice_service.py::TestInvoiceCreation::test_create_invoice_custom_payment_terms", "tests/test_invoice_service.py::TestInvoiceCreation::test_create_invoice_success", "tests/test_invoice_service.py::TestInvoiceDeletion::test_delete_invoice_not_draft", "tests/test_invoice_service.py::TestInvoiceDeletion::test_delete_invoice_not_found", "tests/test_invoice_service.py::TestInvoiceDeletion::test_delete_invoice_success", "tests/test_invoice_service.py::TestInvoiceNumberGeneration::test_generate_invoice_number_first", "tests/test_invoice_service.py::TestInvoiceNumberGeneration::test_generate_invoice_number_sequence", "tests/test_invoice_service.py::TestInvoiceRetrieval::test_get_invoice_by_number_not_found", "tests/test_invoice_service.py::TestInvoiceRetrieval::test_get_invoice_by_number_success", "tests/test_invoice_service.py::TestInvoiceRetrieval::test_get_invoice_not_found", "tests/test_invoice_service.py::TestInvoiceRetrieval::test_get_invoice_success", "tests/test_invoice_service.py::TestInvoiceStatusManagement::test_update_invoice_status_draft_to_issued", "tests/test_invoice_service.py::TestInvoiceStatusManagement::test_update_invoice_status_invalid_transition", "tests/test_invoice_service.py::TestInvoiceStatusManagement::test_update_invoice_status_issued_to_paid", "tests/test_invoice_service.py::TestInvoiceStatusManagement::test_update_invoice_status_paid_without_date", "tests/test_invoice_service.py::TestInvoiceUpdate::test_update_invoice_not_draft", "tests/test_invoice_service.py::TestInvoiceUpdate::test_update_invoice_not_found", "tests/test_invoice_service.py::TestInvoiceUpdate::test_update_invoice_success", "tests/test_tax_api.py::TestTaxCalculationAPI::test_calculate_detailed_tax_summary", "tests/test_tax_api.py::TestTaxCalculationAPI::test_calculate_monthly_tax_summary", "tests/test_tax_api.py::TestTaxCalculationAPI::test_calculate_monthly_tax_summary_get", "tests/test_tax_api.py::TestTaxCalculationAPI::test_calculate_pit_get_method", "tests/test_tax_api.py::TestTaxCalculationAPI::test_calculate_pit_with_company", "tests/test_tax_api.py::TestTaxCalculationAPI::test_calculate_vat_get_method", "tests/test_tax_api.py::TestTaxCalculationAPI::test_calculate_vat_no_company", "tests/test_tax_api.py::TestTaxCalculationAPI::test_calculate_vat_with_company", "tests/test_tax_api.py::TestTaxCalculationAPI::test_compare_tax_options", "tests/test_tax_api.py::TestTaxCalculationAPI::test_invalid_year_month", "tests/test_tax_api.py::TestTaxCalculationAPI::test_negative_income", "tests/test_tax_service.py::TestMonthlyTaxSummary::test_calculate_monthly_tax_summary", "tests/test_tax_service.py::TestPITCalculation::test_calculate_monthly_pit_ryczalt", "tests/test_tax_service.py::TestPITCalculation::test_calculate_monthly_pit_with_expenses", "tests/test_tax_service.py::TestTaxCalculationEdgeCases::test_calculate_pit_company_not_found", "tests/test_tax_service.py::TestTaxCalculationEdgeCases::test_calculate_pit_with_high_zus_deduction", "tests/test_tax_service.py::TestTaxCalculationEdgeCases::test_calculate_pit_zero_income", "tests/test_tax_service.py::TestTaxCalculationEdgeCases::test_calculate_vat_company_not_found", "tests/test_tax_service.py::TestTaxCalculationUtils::test_calculate_pit_for_income_and_type_liniowy", "tests/test_tax_service.py::TestTaxCalculationUtils::test_calculate_pit_for_income_and_type_progresywny", "tests/test_tax_service.py::TestTaxCalculationUtils::test_calculate_pit_for_income_and_type_ryczalt", "tests/test_tax_service.py::TestTaxCalculationUtils::test_round_to_grosz", "tests/test_tax_service.py::TestVATCalculation::test_calculate_monthly_vat_no_expenses", "tests/test_tax_service.py::TestVATCalculation::test_calculate_monthly_vat_with_expenses", "tests/test_zus_api.py::TestZUSCalculationAPI::test_calculate_detailed_zus", "tests/test_zus_api.py::TestZUSCalculationAPI::test_calculate_monthly_zus_get_method", "tests/test_zus_api.py::TestZUSCalculationAPI::test_calculate_monthly_zus_no_company", "tests/test_zus_api.py::TestZUSCalculationAPI::test_calculate_monthly_zus_with_company", "tests/test_zus_api.py::TestZUSCalculationAPI::test_calculate_monthly_zus_without_income", "tests/test_zus_api.py::TestZUSCalculationAPI::test_calculate_yearly_zus", "tests/test_zus_api.py::TestZUSCalculationAPI::test_calculate_yearly_zus_get_method", "tests/test_zus_api.py::TestZUSCalculationAPI::test_calculate_yearly_zus_invalid_year", "tests/test_zus_api.py::TestZUSCalculationAPI::test_calculate_zus_with_custom_date", "tests/test_zus_api.py::TestZUSCalculationAPI::test_calculate_zus_with_negative_income", "tests/test_zus_service.py::TestZUSCalculationEdgeCases::test_calculate_contribution_with_large_amounts", "tests/test_zus_service.py::TestZUSCalculationEdgeCases::test_calculate_contribution_with_very_small_amounts", "tests/test_zus_service.py::TestZUSCalculationEdgeCases::test_health_insurance_edge_cases", "tests/test_zus_service.py::TestZUSCalculationService::test_calculate_monthly_zus", "tests/test_zus_service.py::TestZUSCalculationService::test_calculate_monthly_zus_company_not_found", "tests/test_zus_service.py::TestZUSCalculationService::test_calculate_monthly_zus_no_optional_contributions", "tests/test_zus_service.py::TestZUSCalculationService::test_calculate_yearly_zus_summary", "tests/test_zus_service.py::TestZUSCalculationService::test_zus_calculation_result_to_dict", "tests/test_zus_service.py::TestZUSCalculationUtils::test_calculate_contribution_amount", "tests/test_zus_service.py::TestZUSCalculationUtils::test_calculate_health_insurance", "tests/test_zus_service.py::TestZUSCalculationUtils::test_round_to_grosz"]