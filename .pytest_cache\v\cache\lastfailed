{"tests/test_zus_api.py::TestZUSCalculationAPI::test_calculate_monthly_zus_with_company": true, "tests/test_client_api.py::TestClientAPI::test_create_client": true, "tests/test_client_api.py::TestClientAPI::test_create_client_invalid_nip": true, "tests/test_client_api.py::TestClientAPI::test_create_client_without_nip": true, "tests/test_client_api.py::TestClientAPI::test_get_clients_empty": true, "tests/test_client_api.py::TestClientAPI::test_get_clients_with_data": true, "tests/test_client_api.py::TestClientAPI::test_get_clients_with_pagination": true, "tests/test_client_api.py::TestClientAPI::test_get_clients_with_search": true, "tests/test_client_api.py::TestClientAPI::test_get_client_by_id": true, "tests/test_client_api.py::TestClientAPI::test_get_client_by_id_not_found": true, "tests/test_client_api.py::TestClientAPI::test_update_client": true, "tests/test_client_api.py::TestClientAPI::test_update_client_not_found": true, "tests/test_client_api.py::TestClientAPI::test_delete_client": true, "tests/test_client_api.py::TestClientAPI::test_delete_client_not_found": true, "tests/test_client_api.py::TestClientAPI::test_get_clients_summary": true, "tests/test_client_api.py::TestClientAPI::test_search_client_by_nip": true, "tests/test_company_api.py::TestCompanyAPI::test_create_company_profile": true, "tests/test_company_api.py::TestCompanyAPI::test_create_company_profile_invalid_nip": true, "tests/test_company_api.py::TestCompanyAPI::test_get_company_profile_not_exists": true, "tests/test_company_api.py::TestCompanyAPI::test_get_company_profile_exists": true, "tests/test_company_api.py::TestCompanyAPI::test_update_company_profile": true, "tests/test_company_api.py::TestCompanyAPI::test_update_company_profile_not_exists": true, "tests/test_company_api.py::TestCompanyAPI::test_get_tax_settings_no_company": true, "tests/test_company_api.py::TestCompanyAPI::test_get_tax_settings_with_company": true, "tests/test_company_api.py::TestCompanyAPI::test_update_tax_settings": true, "tests/test_company_api.py::TestCompanyAPI::test_get_zus_settings_with_company": true, "tests/test_expense_api.py::TestExpenseAPI::test_create_expense_with_net_amount": true, "tests/test_expense_api.py::TestExpenseAPI::test_create_expense_with_gross_amount": true, "tests/test_expense_api.py::TestExpenseAPI::test_create_expense_invalid_data": true, "tests/test_expense_api.py::TestExpenseAPI::test_create_expense_future_date": true, "tests/test_expense_api.py::TestExpenseAPI::test_get_expenses_empty": true, "tests/test_expense_api.py::TestExpenseAPI::test_get_expenses_with_data": true, "tests/test_expense_api.py::TestExpenseAPI::test_get_expenses_with_pagination": true, "tests/test_expense_api.py::TestExpenseAPI::test_get_expenses_with_search": true, "tests/test_expense_api.py::TestExpenseAPI::test_get_expenses_with_filters": true, "tests/test_expense_api.py::TestExpenseAPI::test_get_expense_by_id": true, "tests/test_expense_api.py::TestExpenseAPI::test_get_expense_by_id_not_found": true, "tests/test_expense_api.py::TestExpenseAPI::test_update_expense": true, "tests/test_expense_api.py::TestExpenseAPI::test_update_expense_not_found": true, "tests/test_expense_api.py::TestExpenseAPI::test_delete_expense": true, "tests/test_expense_api.py::TestExpenseAPI::test_delete_expense_not_found": true, "tests/test_expense_api.py::TestExpenseAPI::test_get_expense_summary": true, "tests/test_expense_api.py::TestExpenseAPI::test_get_expense_categories": true, "tests/test_expense_api.py::TestExpenseAPI::test_get_payment_methods": true, "tests/test_expense_service.py::TestExpenseSummary::test_get_expense_summary": true, "tests/test_invoice_api.py::TestInvoiceAPI::test_create_invoice_success": true, "tests/test_invoice_api.py::TestInvoiceAPI::test_create_invoice_client_not_found": true, "tests/test_invoice_api.py::TestInvoiceAPI::test_create_invoice_validation_errors": true, "tests/test_invoice_api.py::TestInvoiceAPI::test_get_invoice_success": true, "tests/test_invoice_api.py::TestInvoiceAPI::test_get_invoice_not_found": true, "tests/test_invoice_api.py::TestInvoiceAPI::test_get_invoice_by_number_success": true, "tests/test_invoice_api.py::TestInvoiceAPI::test_list_invoices_empty": true, "tests/test_invoice_api.py::TestInvoiceAPI::test_list_invoices_with_data": true, "tests/test_invoice_api.py::TestInvoiceAPI::test_list_invoices_with_filters": true, "tests/test_invoice_api.py::TestInvoiceAPI::test_update_invoice_success": true, "tests/test_invoice_api.py::TestInvoiceAPI::test_update_invoice_not_draft": true, "tests/test_invoice_api.py::TestInvoiceAPI::test_issue_invoice_success": true, "tests/test_invoice_api.py::TestInvoiceAPI::test_mark_invoice_paid_success": true, "tests/test_invoice_api.py::TestInvoiceAPI::test_cancel_invoice_success": true, "tests/test_invoice_api.py::TestInvoiceAPI::test_delete_invoice_success": true, "tests/test_invoice_api.py::TestInvoiceAPI::test_delete_invoice_not_draft": true, "tests/test_invoice_api.py::TestInvoiceAPI::test_get_invoice_summary_empty": true, "tests/test_invoice_api.py::TestInvoiceAPI::test_get_invoice_summary_with_data": true, "tests/test_invoice_service.py::TestInvoiceNumberGeneration::test_generate_invoice_number_sequence": true, "tests/test_invoice_service.py::TestInvoiceCreation::test_create_invoice_success": true, "tests/test_invoice_service.py::TestInvoiceCreation::test_create_invoice_custom_payment_terms": true, "tests/test_invoice_service.py::TestInvoiceRetrieval::test_get_invoice_success": true, "tests/test_invoice_service.py::TestInvoiceRetrieval::test_get_invoice_by_number_success": true, "tests/test_invoice_service.py::TestInvoiceUpdate::test_update_invoice_success": true, "tests/test_invoice_service.py::TestInvoiceUpdate::test_update_invoice_not_draft": true, "tests/test_invoice_service.py::TestInvoiceStatusManagement::test_update_invoice_status_draft_to_issued": true, "tests/test_invoice_service.py::TestInvoiceStatusManagement::test_update_invoice_status_issued_to_paid": true, "tests/test_invoice_service.py::TestInvoiceStatusManagement::test_update_invoice_status_invalid_transition": true, "tests/test_invoice_service.py::TestInvoiceStatusManagement::test_update_invoice_status_paid_without_date": true, "tests/test_invoice_service.py::TestInvoiceDeletion::test_delete_invoice_success": true, "tests/test_invoice_service.py::TestInvoiceDeletion::test_delete_invoice_not_draft": true, "tests/test_tax_api.py::TestTaxCalculationAPI::test_calculate_vat_no_company": true, "tests/test_tax_api.py::TestTaxCalculationAPI::test_calculate_vat_with_company": true, "tests/test_tax_api.py::TestTaxCalculationAPI::test_calculate_vat_get_method": true, "tests/test_tax_api.py::TestTaxCalculationAPI::test_calculate_pit_with_company": true, "tests/test_tax_api.py::TestTaxCalculationAPI::test_calculate_pit_get_method": true, "tests/test_tax_api.py::TestTaxCalculationAPI::test_calculate_monthly_tax_summary": true, "tests/test_tax_api.py::TestTaxCalculationAPI::test_calculate_monthly_tax_summary_get": true, "tests/test_tax_api.py::TestTaxCalculationAPI::test_calculate_detailed_tax_summary": true, "tests/test_tax_api.py::TestTaxCalculationAPI::test_compare_tax_options": true, "tests/test_tax_api.py::TestTaxCalculationAPI::test_invalid_year_month": true, "tests/test_tax_api.py::TestTaxCalculationAPI::test_negative_income": true, "tests/test_tax_service.py::TestVATCalculation::test_calculate_monthly_vat_no_expenses": true, "tests/test_tax_service.py::TestVATCalculation::test_calculate_monthly_vat_with_expenses": true, "tests/test_tax_service.py::TestPITCalculation::test_calculate_monthly_pit_ryczalt": true, "tests/test_tax_service.py::TestPITCalculation::test_calculate_monthly_pit_with_expenses": true, "tests/test_tax_service.py::TestMonthlyTaxSummary::test_calculate_monthly_tax_summary": true, "tests/test_zus_api.py::TestZUSCalculationAPI::test_calculate_monthly_zus_no_company": true, "tests/test_zus_api.py::TestZUSCalculationAPI::test_calculate_monthly_zus_get_method": true, "tests/test_zus_api.py::TestZUSCalculationAPI::test_calculate_monthly_zus_without_income": true, "tests/test_zus_api.py::TestZUSCalculationAPI::test_calculate_detailed_zus": true, "tests/test_zus_api.py::TestZUSCalculationAPI::test_calculate_yearly_zus": true, "tests/test_zus_api.py::TestZUSCalculationAPI::test_calculate_yearly_zus_get_method": true, "tests/test_zus_api.py::TestZUSCalculationAPI::test_calculate_yearly_zus_invalid_year": true, "tests/test_zus_api.py::TestZUSCalculationAPI::test_calculate_zus_with_negative_income": true, "tests/test_zus_api.py::TestZUSCalculationAPI::test_calculate_zus_with_custom_date": true}